<template>
  <view class="home-container">
    <!-- 现代化头部区域 -->
    <view class="hero-section">
      <view class="hero-content">
        <view class="hero-text">
          <text class="hero-title">智能字幕胶囊</text>
          <text class="hero-subtitle">AI驱动的视频字幕生成工具</text>
          <text class="hero-description">让每个视频都拥有精准的字幕，提升观看体验</text>
        </view>
        <view class="hero-image">
          <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 快速开始按钮 -->
      <view class="hero-action">
        <button @click="startUpload" class="btn btn-primary btn-lg start-btn">
          <text class="btn-icon">🚀</text>
          <text>开始制作</text>
        </button>
        <text class="hero-tip">支持MP4、MOV等格式，最大100MB</text>
      </view>
    </view>

    <!-- 功能特性卡片 -->
    <view class="features-section">
      <view class="section-header">
        <text class="section-title">核心功能</text>
        <text class="section-subtitle">AI智能处理，简单高效</text>
      </view>

      <view class="features-grid">
        <view class="feature-card" v-for="(feature, index) in features" :key="index">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">{{ feature.icon }}</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">{{ feature.title }}</text>
            <text class="feature-desc">{{ feature.desc }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近处理 -->
    <view v-if="recentTasks.length > 0" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近处理</text>
        <text class="more-link" @click="viewAllHistory">查看全部 →</text>
      </view>

      <view class="recent-cards">
        <view v-for="task in recentTasks" :key="task._id" class="recent-card card" @click="viewTaskDetail(task)">
          <view class="recent-card-content">
            <view class="task-info">
              <text class="task-name text-ellipsis">{{ task.fileName || '未命名视频' }}</text>
              <text class="task-time">{{ formatTime(task.createTime) }}</text>
            </view>
            <view class="task-status-wrapper">
              <view class="tag" :class="getStatusTagClass(task.status)">
                <text>{{ getStatusText(task.status) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用指南 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">使用指南</text>
        <text class="section-subtitle">三步完成视频字幕制作</text>
      </view>

      <view class="guide-steps">
        <view class="guide-step" v-for="(step, index) in guideSteps" :key="index">
          <view class="step-indicator">
            <text class="step-number">{{ index + 1 }}</text>
          </view>
          <view class="step-content">
            <text class="step-title">{{ step.title }}</text>
            <text class="step-desc">{{ step.desc }}</text>
          </view>
          <view v-if="index < guideSteps.length - 1" class="step-connector"></view>
        </view>
      </view>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const recentTasks = ref<any[]>([])

// 功能特性数据
const features = ref([
  {
    icon: '🎬',
    title: '视频上传',
    desc: '支持多种格式，快速上传'
  },
  {
    icon: '🎤',
    title: '语音识别',
    desc: 'AI智能识别语音内容'
  },
  {
    icon: '📝',
    title: '字幕生成',
    desc: '自动翻译生成精准字幕'
  },
  {
    icon: '🎯',
    title: '精准同步',
    desc: '字幕与视频完美同步'
  }
])

// 使用指南数据
const guideSteps = ref([
  {
    title: '选择视频',
    desc: '上传您的视频文件，支持多种格式'
  },
  {
    title: 'AI处理',
    desc: '智能识别语音并生成字幕'
  },
  {
    title: '下载结果',
    desc: '预览并下载带字幕的视频'
  }
])

// 页面加载时获取最近任务
onMounted(() => {
  loadRecentTasks()
})

// 加载最近任务 - 显示功能开发中提示
const loadRecentTasks = async () => {
  try {
    // 模拟数据，实际API已移除
    recentTasks.value = []
    console.log('视频处理功能开发中，暂无历史数据')
  } catch (error) {
    console.error('加载最近任务失败:', error)
  }
}

// 开始上传 - 显示功能开发中提示
const startUpload = () => {
  uni.showModal({
    title: '功能开发中',
    content: '视频上传功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 查看所有历史 - 显示功能开发中提示
const viewAllHistory = () => {
  uni.navigateTo({
    url: '/pages/history/history'
  })
}

// 查看任务详情 - 显示功能开发中提示
const viewTaskDetail = (task: any) => {
  uni.showModal({
    title: '功能开发中',
    content: '任务详情功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 获取状态标签样式类
const getStatusTagClass = (status: string): string => {
  switch (status) {
    case 'processing': return 'tag-primary'
    case 'completed': return 'tag-success'
    case 'failed': return 'tag-error'
    default: return 'tag-primary'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`

  return `${date.getMonth() + 1}-${date.getDate()}`
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding-bottom: 120rpx;
  /* 为tabbar留出空间 */
}

/* 现代化头部区域 */
.hero-section {
  padding: 60rpx 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0 0 48rpx 48rpx;
  margin-bottom: 48rpx;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48rpx;
  position: relative;
  z-index: 1;
}

.hero-text {
  flex: 1;
  margin-right: 32rpx;
}

.hero-title {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.hero-subtitle {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
}

.hero-description {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.hero-image {
  flex-shrink: 0;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.hero-action {
  text-align: center;
  position: relative;
  z-index: 1;
}

.start-btn {
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}

.cta-button:active {
  background: linear-gradient(135deg, #ee5a52, #dc4c64);
  transform: translateY(-2rpx);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.hero-tip {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 16rpx;
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.user-status {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 功能特性区域 */
.features-section {
  padding: 0 32rpx 48rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.section-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.section-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.feature-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

}

.feature-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
  border-color: #e0e7ff;
}


.feature-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.feature-icon {
  font-size: 40rpx;
}

.feature-content {
  text-align: left;
}

.feature-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.feature-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 状态区域 */
.status-section {
  padding: 0 32rpx 48rpx;
}

.status-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

.status-value.success {
  color: #10b981;
}

/* 指南区域 */
.guide-section {
  padding: 0 32rpx 48rpx;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.guide-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.guide-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  font-weight: 700;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.guide-text {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.guide-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
}

.recent-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.recent-section .section-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}

.more-link {
  font-size: 28rpx;
  color: #6366f1;
  font-weight: 500;
}

.recent-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

}

.guide-step:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.recent-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  flex: 1;
  margin-right: 24rpx;
}

.task-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.task-time {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
}

.task-status-wrapper {
  flex-shrink: 0;
}

/* 使用指南区域 */
.guide-section {
  padding: 0 32rpx 48rpx;
}

.guide-section .section-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.guide-steps {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  position: relative;
  margin-bottom: 48rpx;

}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-indicator {
  flex-shrink: 0;
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
}

.step-content {
  flex: 1;
  padding-top: 12rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.step-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.step-connector {
  position: absolute;
  left: 40rpx;
  top: 80rpx;
  width: 4rpx;
  height: 48rpx;
  background: linear-gradient(180deg, #e0e7ff, #c7d2fe);
  border-radius: 2rpx;
  z-index: 1;
}

/* 底部空间 */
.bottom-space {
  height: 48rpx;
}
</style>
