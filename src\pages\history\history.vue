<template>
  <view class="history-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <text class="search-icon">🔍</text>
        <input class="search-input" placeholder="搜索历史记录" v-model="searchKeyword" />
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          :class="{ active: activeFilter === filter.value }"
          v-for="filter in filters" 
          :key="filter.value"
          @click="setActiveFilter(filter.value)"
        >
          <text>{{ filter.label }}</text>
        </view>
      </view>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-list">
      <view v-if="filteredTasks.length === 0" class="empty-state">
        <text class="empty-icon">📝</text>
        <text class="empty-title">暂无历史记录</text>
        <text class="empty-desc">开始上传视频，创建您的第一个字幕任务</text>
        <button @click="goToUpload" class="btn btn-primary">
          <text>开始上传</text>
        </button>
      </view>

      <view v-else class="task-list">
        <view 
          class="task-item" 
          v-for="task in filteredTasks" 
          :key="task.id"
          @click="viewTaskDetail(task)"
        >
          <view class="task-content">
            <view class="task-info">
              <text class="task-name">{{ task.fileName }}</text>
              <text class="task-time">{{ formatTime(task.createTime) }}</text>
            </view>
            <view class="task-status">
              <view class="status-tag" :class="getStatusClass(task.status)">
                <text>{{ getStatusText(task.status) }}</text>
              </view>
            </view>
          </view>
          <view class="task-meta">
            <text class="task-size">{{ formatFileSize(task.fileSize) }}</text>
            <text class="task-duration">{{ formatDuration(task.duration) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const activeFilter = ref('all')

// 筛选选项
const filters = ref([
  { label: '全部', value: 'all' },
  { label: '处理中', value: 'processing' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
])

// 示例任务数据
const tasks = ref([
  {
    id: '1',
    fileName: '示例视频1.mp4',
    status: 'completed',
    createTime: '2024-01-15T10:30:00Z',
    fileSize: 25600000,
    duration: 120
  },
  {
    id: '2',
    fileName: '示例视频2.mov',
    status: 'processing',
    createTime: '2024-01-15T09:15:00Z',
    fileSize: 45800000,
    duration: 180
  },
  {
    id: '3',
    fileName: '示例视频3.mp4',
    status: 'failed',
    createTime: '2024-01-14T16:45:00Z',
    fileSize: 12300000,
    duration: 90
  }
])

// 计算过滤后的任务列表
const filteredTasks = computed(() => {
  let result = tasks.value

  // 按状态筛选
  if (activeFilter.value !== 'all') {
    result = result.filter(task => task.status === activeFilter.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    result = result.filter(task => 
      task.fileName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return result
})

onMounted(() => {
  // 显示功能开发中提示
  uni.showModal({
    title: '功能开发中',
    content: '历史记录功能正在开发中，当前显示为演示数据',
    showCancel: false
  })
})

// 设置活动筛选器
const setActiveFilter = (filter: string) => {
  activeFilter.value = filter
}

// 查看任务详情
const viewTaskDetail = (task: any) => {
  if (task.status === 'completed') {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${task.id}`
    })
  } else if (task.status === 'processing') {
    uni.navigateTo({
      url: `/pages/process/process?taskId=${task.id}`
    })
  } else {
    uni.showModal({
      title: '任务失败',
      content: '该任务处理失败，是否重新处理？',
      success: (res) => {
        if (res.confirm) {
          // 重新处理逻辑
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  }
}

// 跳转到上传页面
const goToUpload = () => {
  uni.switchTab({
    url: '/pages/upload/upload'
  })
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 获取状态样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'processing': return 'status-processing'
    case 'completed': return 'status-completed'
    case 'failed': return 'status-failed'
    default: return 'status-unknown'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`

  return `${date.getMonth() + 1}-${date.getDate()}`
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.history-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding-bottom: 120rpx;
}

.search-section {
  padding: 32rpx;
  padding-bottom: 16rpx;
}

.search-bar {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.search-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
}

.filter-section {
  padding: 0 32rpx 16rpx;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
}

.filter-tab {
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #6b7280;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #6366f1;
  color: white;
}

.history-list {
  padding: 0 32rpx;
}

.empty-state {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-item {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.task-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.task-info {
  flex: 1;
}

.task-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.task-time {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.task-status {
  
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-processing {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-failed {
  background: #fee2e2;
  color: #dc2626;
}

.task-meta {
  display: flex;
  gap: 24rpx;
  font-size: 24rpx;
  color: #6b7280;
}

.btn {
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.bottom-space {
  height: 80rpx;
}
</style>
