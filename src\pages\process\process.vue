<template>
  <view class="process-container">
    <!-- 进度卡片 -->
    <view class="progress-card">
      <view class="progress-header">
        <text class="progress-title">处理进度</text>
        <text class="progress-status">{{ statusText }}</text>
      </view>
      
      <view class="progress-bar-container">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress + '%' }"></view>
        </view>
        <text class="progress-text">{{ progress }}%</text>
      </view>

      <view class="progress-steps">
        <view class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <view class="step-icon">📤</view>
          <text class="step-text">上传视频</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <view class="step-icon">🎤</view>
          <text class="step-text">语音识别</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
          <view class="step-icon">📝</view>
          <text class="step-text">生成字幕</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
          <view class="step-icon">🎬</view>
          <text class="step-text">合成视频</text>
        </view>
      </view>
    </view>

    <!-- 视频信息 -->
    <view class="video-info-card">
      <view class="card-header">
        <text class="card-title">视频信息</text>
      </view>
      <view class="card-body">
        <view class="info-item">
          <text class="info-label">文件名:</text>
          <text class="info-value">{{ videoInfo.fileName || '示例视频.mp4' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">文件大小:</text>
          <text class="info-value">{{ formatFileSize(videoInfo.fileSize || 25600000) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">视频时长:</text>
          <text class="info-value">{{ formatDuration(videoInfo.duration || 120) }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button v-if="isCompleted" @click="viewResult" class="btn btn-primary">
        查看结果
      </button>
      <button v-else @click="cancelProcess" class="btn btn-secondary">
        取消处理
      </button>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const progress = ref(0)
const currentStep = ref(1)
const statusText = ref('正在处理中...')
const isCompleted = ref(false)

// 视频信息
const videoInfo = ref({
  fileName: '',
  fileSize: 0,
  duration: 0
})

// 模拟进度更新
let progressTimer: number | null = null

onMounted(() => {
  // 显示功能开发中提示
  uni.showModal({
    title: '功能开发中',
    content: '视频处理功能正在开发中，当前显示为演示界面',
    showCancel: false
  })
  
  // 模拟进度更新
  startProgressSimulation()
})

onUnmounted(() => {
  if (progressTimer) {
    clearInterval(progressTimer)
  }
})

// 开始进度模拟
const startProgressSimulation = () => {
  progressTimer = setInterval(() => {
    if (progress.value < 100) {
      progress.value += Math.random() * 5
      if (progress.value > 100) progress.value = 100
      
      // 更新步骤
      if (progress.value > 25) currentStep.value = 2
      if (progress.value > 50) currentStep.value = 3
      if (progress.value > 75) currentStep.value = 4
      
      // 更新状态文本
      if (progress.value < 25) {
        statusText.value = '正在上传视频...'
      } else if (progress.value < 50) {
        statusText.value = '正在识别语音...'
      } else if (progress.value < 75) {
        statusText.value = '正在生成字幕...'
      } else if (progress.value < 100) {
        statusText.value = '正在合成视频...'
      } else {
        statusText.value = '处理完成'
        isCompleted.value = true
        if (progressTimer) {
          clearInterval(progressTimer)
        }
      }
    }
  }, 1000)
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 查看结果
const viewResult = () => {
  uni.navigateTo({
    url: '/pages/result/result'
  })
}

// 取消处理
const cancelProcess = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消当前处理吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}
</script>

<style scoped>
.process-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.progress-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.progress-status {
  font-size: 28rpx;
  color: #6366f1;
  font-weight: 500;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 48rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  min-width: 80rpx;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step.completed {
  opacity: 0.8;
}

.step-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.step-text {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}

.video-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #6b7280;
}

.info-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.action-section {
  display: flex;
  justify-content: center;
}

.btn {
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  min-width: 200rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.bottom-space {
  height: 80rpx;
}
</style>
