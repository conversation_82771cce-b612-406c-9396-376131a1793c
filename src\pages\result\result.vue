<template>
  <view class="result-container">
    <!-- 成功提示 -->
    <view class="success-card">
      <view class="success-icon">✅</view>
      <text class="success-title">处理完成</text>
      <text class="success-desc">您的视频已成功生成字幕</text>
    </view>

    <!-- 视频预览 -->
    <view class="video-card">
      <view class="card-header">
        <text class="card-title">视频预览</text>
      </view>
      <view class="video-preview">
        <view class="video-placeholder">
          <text class="video-icon">🎬</text>
          <text class="video-text">视频预览</text>
          <text class="video-desc">功能开发中</text>
        </view>
      </view>
    </view>

    <!-- 字幕预览 -->
    <view class="subtitle-card">
      <view class="card-header">
        <text class="card-title">字幕预览</text>
      </view>
      <view class="subtitle-content">
        <view class="subtitle-item" v-for="(item, index) in subtitles" :key="index">
          <text class="subtitle-time">{{ item.startTime }} - {{ item.endTime }}</text>
          <text class="subtitle-text">{{ item.text }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button @click="downloadVideo" class="btn btn-primary">
        <text class="btn-icon">📥</text>
        <text>下载视频</text>
      </button>
      <button @click="downloadSubtitle" class="btn btn-secondary">
        <text class="btn-icon">📄</text>
        <text>下载字幕</text>
      </button>
    </view>

    <!-- 分享按钮 -->
    <view class="share-section">
      <button @click="shareResult" class="btn btn-outline">
        <text class="btn-icon">📤</text>
        <text>分享结果</text>
      </button>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 示例字幕数据
const subtitles = ref([
  {
    startTime: '00:00:01',
    endTime: '00:00:03',
    text: '欢迎使用智能字幕胶囊'
  },
  {
    startTime: '00:00:04',
    endTime: '00:00:06',
    text: 'AI驱动的视频字幕生成工具'
  },
  {
    startTime: '00:00:07',
    endTime: '00:00:09',
    text: '让每个视频都拥有精准的字幕'
  }
])

onMounted(() => {
  // 显示功能开发中提示
  uni.showModal({
    title: '功能开发中',
    content: '视频处理功能正在开发中，当前显示为演示界面',
    showCancel: false
  })
})

// 下载视频
const downloadVideo = () => {
  uni.showModal({
    title: '功能开发中',
    content: '视频下载功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 下载字幕
const downloadSubtitle = () => {
  uni.showModal({
    title: '功能开发中',
    content: '字幕下载功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 分享结果
const shareResult = () => {
  uni.showModal({
    title: '功能开发中',
    content: '分享功能正在开发中，敬请期待！',
    showCancel: false
  })
}
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.success-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.video-card, .subtitle-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.video-preview {
  aspect-ratio: 16/9;
  background: #f3f4f6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-placeholder {
  text-align: center;
}

.video-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}

.video-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.video-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.subtitle-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.subtitle-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-time {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.share-section {
  display: flex;
  justify-content: center;
}

.btn {
  flex: 1;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.btn-outline {
  background: transparent;
  color: #6366f1;
  border: 2rpx solid #6366f1;
  min-width: 200rpx;
}

.btn-icon {
  font-size: 28rpx;
}

.bottom-space {
  height: 80rpx;
}
</style>
