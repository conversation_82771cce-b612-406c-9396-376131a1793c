<template>
  <view class="upload-container">
    <!-- 上传区域 -->
    <view class="upload-section">
      <view class="upload-area" @click="selectVideo">
        <view class="upload-icon">📹</view>
        <text class="upload-title">选择视频文件</text>
        <text class="upload-desc">支持MP4、MOV等格式，最大100MB</text>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="info-section">
      <view class="info-card">
        <view class="info-header">
          <text class="info-title">支持格式</text>
        </view>
        <view class="info-content">
          <text class="info-text">MP4、MOV、AVI、WMV等主流视频格式</text>
        </view>
      </view>

      <view class="info-card">
        <view class="info-header">
          <text class="info-title">文件大小</text>
        </view>
        <view class="info-content">
          <text class="info-text">建议文件大小不超过100MB</text>
        </view>
      </view>

      <view class="info-card">
        <view class="info-header">
          <text class="info-title">处理时间</text>
        </view>
        <view class="info-content">
          <text class="info-text">根据视频长度，通常需要1-5分钟</text>
        </view>
      </view>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
// 选择视频文件 - 显示功能开发中提示
const selectVideo = () => {
  uni.showModal({
    title: '功能开发中',
    content: '视频上传功能正在开发中，敬请期待！',
    showCancel: false
  })
}
</script>

<style scoped>
.upload-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx dashed #d1d5db;
  transition: all 0.3s ease;
}

.upload-area:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.upload-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.upload-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.info-header {
  margin-bottom: 16rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.info-content {
  
}

.info-text {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.bottom-space {
  height: 80rpx;
}
</style>
